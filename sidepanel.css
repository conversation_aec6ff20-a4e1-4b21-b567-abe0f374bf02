* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 20px;
    margin-bottom: 4px;
}

.header p {
    font-size: 12px;
    opacity: 0.9;
}

.tabs-container {
    display: flex;
    align-items: center;
    background-color: #e0e0e0;
    padding: 4px 0;
    border-bottom: 1px solid #ccc;
    overflow-x: auto;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* IE and Edge */
}

.tabs-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.tabs {
    display: flex;
    flex-grow: 1;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tabs::-webkit-scrollbar {
    display: none;
}

.tab {
    display: inline-flex;
    align-items: center;
    padding: 6px 10px;
    margin-left: 4px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    font-size: 12px;
    color: #555;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: background-color 0.2s, color 0.2s;
    position: relative;
}

.tab:hover {
    background-color: #e8e8e8;
}

.tab.active {
    background-color: white;
    border-color: #667eea;
    color: #333;
    font-weight: bold;
    z-index: 1;
}

.tab-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
    padding-right: 5px; /* Space for close button */
}

.close-tab-btn {
    background: none;
    border: none;
    color: #888;
    font-size: 14px;
    cursor: pointer;
    margin-left: 5px;
    padding: 0 2px;
    line-height: 1;
    transition: color 0.2s;
}

.close-tab-btn:hover {
    color: #333;
}

.new-tab-btn {
    background-color: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 14px;
    cursor: pointer;
    margin-right: 8px;
    flex-shrink: 0;
    transition: background-color 0.2s;
}

.new-tab-btn:hover {
    background-color: #5a6fd8;
}

.url-input-section {
    padding: 12px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
}

.input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

#urlInput {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

#urlInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

#loadBtn {
    padding: 8px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

#loadBtn:hover {
    background: #5a6fd8;
}

.quick-actions {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.quick-actions button {
    padding: 6px 10px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.quick-actions button:hover {
    background: #e0e0e0;
}

.quick-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.content-area {
    flex: 1;
    position: relative;
    background: white;
}

#contentFrame {
    width: 100%;
    height: 100%;
    border: none;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.error h3 {
    color: #e74c3c;
    margin-bottom: 8px;
}

.error p {
    color: #666;
    margin-bottom: 16px;
}

#retryBtn {
    padding: 8px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.status-bar {
    background: #f8f9fa;
    padding: 8px 12px;
    border-top: 1px solid #e0e0e0;
    font-size: 12px;
    color: #666;
}

.hidden {
    display: none !important;
}
