# AnySide 安装和使用指南

## 🚀 快速开始

### 系统要求
- Chrome 浏览器版本 114 或更高
- 支持 Chrome Side Panel API

### 安装步骤

1. **下载项目文件**
   - 确保所有文件都在 `AnySide` 文件夹中

2. **打开 Chrome 扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击页面右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 AnySide 项目文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 在 Chrome 工具栏中应该出现 AnySide 图标
   - 扩展列表中显示 AnySide 扩展

## 📱 使用方法

### 基本操作

1. **打开侧边栏**
   - 点击工具栏中的 AnySide 图标
   - 或在弹窗中点击"打开侧边栏"

2. **浏览网页**
   - 在地址栏输入网址
   - 点击"加载"按钮或按回车键

3. **导航控制**
   - 🔄 刷新：重新加载当前页面
   - ⬅️ 后退：返回上一页
   - ➡️ 前进：前往下一页
   - 🏠 主页：返回默认主页

### 高级功能

1. **加载当前页面**
   - 点击扩展图标
   - 选择"在侧边栏中加载当前页面"

2. **个性化设置**
   - 点击扩展弹窗中的"设置"
   - 配置默认主页、主题等选项

## ⚙️ 配置选项

### 基本设置
- **默认主页**：设置侧边栏的起始页面
- **自动加载**：打开侧边栏时自动加载默认页面
- **主题**：选择浅色、深色或跟随系统

### 高级设置
- **缩放级别**：调整页面显示比例
- **阻止弹窗**：防止页面打开新窗口
- **用户代理**：自定义浏览器标识

## 🧪 测试建议

### 推荐测试网站
- Google 搜索：https://www.google.com
- 维基百科：https://www.wikipedia.org
- GitHub：https://www.github.com
- MDN 文档：https://developer.mozilla.org

### 测试功能
1. 基本页面加载
2. 表单提交和交互
3. 链接导航
4. 多媒体内容
5. 响应式布局

## ❌ 已知限制

### 无法加载的页面类型
- Chrome 内部页面（chrome://）
- 设置了 X-Frame-Options 的网站
- 某些银行和金融网站
- 部分社交媒体登录页面

### 功能限制
- 某些 JavaScript 功能可能受限
- 文件下载可能不可用
- 某些插件内容无法显示

## 🐛 故障排除

### 常见问题

**Q: 扩展图标不显示**
- 检查是否正确加载扩展
- 确认 manifest.json 文件格式正确
- 重新加载扩展

**Q: 侧边栏无法打开**
- 确认 Chrome 版本支持 Side Panel API
- 检查扩展权限设置
- 尝试重启浏览器

**Q: 页面加载失败**
- 检查网络连接
- 确认目标网站可正常访问
- 尝试其他测试网站

**Q: 设置无法保存**
- 检查扩展存储权限
- 清除浏览器缓存
- 重新安装扩展

### 调试方法

1. **查看控制台错误**
   - 右键点击扩展图标 → 检查弹出式窗口
   - 在侧边栏中按 F12 打开开发者工具

2. **检查扩展状态**
   - 访问 `chrome://extensions/`
   - 查看扩展详情和错误信息

3. **重新加载扩展**
   - 在扩展管理页面点击刷新按钮
   - 或删除后重新加载

## 📞 获取帮助

### 技术支持
- 查看项目 README.md 文件
- 打开 test.html 进行功能测试
- 检查浏览器控制台的错误信息

### 反馈问题
- 描述具体的错误现象
- 提供 Chrome 版本信息
- 包含控制台错误日志

## 🔄 更新扩展

1. 下载新版本文件
2. 在扩展管理页面点击"重新加载"
3. 或删除旧版本后重新安装

## 🎯 使用技巧

1. **快速访问**：将常用网站设为默认主页
2. **多任务处理**：在侧边栏浏览参考资料，主窗口进行工作
3. **响应式测试**：调整缩放级别测试网页适配
4. **隐私浏览**：侧边栏浏览不会影响主窗口的浏览历史

祝您使用愉快！🎉
